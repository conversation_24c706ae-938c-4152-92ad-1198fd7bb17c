import { device, expect, element, by, waitFor } from 'detox';

describe('UI Consistency E2E Tests', (): void => {
  beforeEach(async (): Promise<void> => {
    await device.reloadReactNative();
  });

  describe('Theme and Styling', (): void => {
    it('should display consistent styling across all screens', async (): Promise<void> => {
      // Test Tab One styling
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      await expect(element(by.text('Tab One'))).toBeVisible();
      
      // Check that the title has proper styling (bold, larger font)
      const tabOneTitle = element(by.text('Tab One'));
      await expect(tabOneTitle).toBeVisible();
      
      // Test Tab Two styling
      await element(by.text('Tab Two')).tap();
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      const tabTwoTitle = element(by.text('Tab Two'));
      await expect(tabTwoTitle).toBeVisible();
      
      // Test Modal styling
      await element(by.text('Tab One')).tap();
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(5000);
      
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      const modalTitle = element(by.text('Modal'));
      await expect(modalTitle).toBeVisible();
    });

    it('should display MonoText with correct font family', async (): Promise<void> => {
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // The file path should be displayed in MonoText (SpaceMono font)
      await expect(element(by.text('app/(tabs)/index.tsx'))).toBeVisible();
      
      // Switch to Tab Two and check MonoText there
      await element(by.text('Tab Two')).tap();
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('app/(tabs)/two.tsx'))).toBeVisible();
      
      // Check MonoText in modal
      await element(by.text('Tab One')).tap();
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('app/modal.tsx'))).toBeVisible();
    });
  });

  describe('Layout and Spacing', (): void => {
    it('should have consistent layout across screens', async (): Promise<void> => {
      // Test that all screens have the main container with centered content
      
      // Tab One layout
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      await expect(element(by.text('Tab One'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      
      // Tab Two layout
      await element(by.text('Tab Two')).tap();
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('Tab Two'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      
      // Modal layout
      await element(by.text('Tab One')).tap();
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('Modal'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
    });

    it('should display separators correctly', async (): Promise<void> => {
      // Each screen should have a separator line between title and content
      
      // Tab One separator (visual element, hard to test directly)
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // The separator is a View component, so we check content above and below it
      await expect(element(by.text('Tab One'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      
      // Tab Two separator
      await element(by.text('Tab Two')).tap();
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('Tab Two'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
    });
  });

  describe('Interactive Elements', (): void => {
    it('should have properly functioning tab bar', async (): Promise<void> => {
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Tab bar should be visible and functional
      await expect(element(by.text('Tab One'))).toBeTappable();
      await expect(element(by.text('Tab Two'))).toBeTappable();
      
      // Icons should be present (though we can't easily test the actual icons)
      // We can verify the tabs work by tapping them
      await element(by.text('Tab Two')).tap();
      await expect(element(by.text('Tab Two'))).toBeVisible();
      
      await element(by.text('Tab One')).tap();
      await expect(element(by.text('Tab One'))).toBeVisible();
    });

    it('should have functioning header with info button', async (): Promise<void> => {
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Info button should be tappable
      const infoButton = element(by.id('info-button'));
      await expect(infoButton).toBeTappable();
      
      // Tapping should open modal
      await infoButton.tap();
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Text Content Consistency', (): void => {
    it('should display consistent help text across screens', async (): Promise<void> => {
      const helpText: string = 'Change any of the text, save the file, and your app will automatically update.';
      
      // Tab One
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      await expect(element(by.text(helpText))).toBeVisible();
      
      // Tab Two
      await element(by.text('Tab Two')).tap();
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(helpText))).toBeVisible();
      
      // Modal
      await element(by.text('Tab One')).tap();
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(helpText))).toBeVisible();
    });
  });
});
