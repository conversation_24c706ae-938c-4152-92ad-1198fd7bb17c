{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": false, "outDir": "./dist", "rootDir": ".", "types": ["jest", "detox", "node"], "baseUrl": ".", "paths": {"@/*": ["../*"]}}, "include": ["**/*.ts", "**/*.js", "detox.d.ts"], "exclude": ["node_modules", "dist"]}