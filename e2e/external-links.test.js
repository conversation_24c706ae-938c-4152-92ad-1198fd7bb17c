const { device, expect, element, by, waitFor } = require('detox');

describe('External Links E2E Tests', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('External Link Interaction', () => {
    it('should find and interact with external link on Tab One', async () => {
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Look for the external link text
      const linkText = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      
      // Note: We can't actually test the external link opening in e2e tests
      // as it would open a browser, but we can verify the link is tappable
      await expect(element(by.text(linkText))).toBeTappable();
    });

    it('should find external link on Tab Two', async () => {
      await element(by.text('Tab Two')).tap();
      
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      const linkText = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      await expect(element(by.text(linkText))).toBeTappable();
    });

    it('should find external link in modal', async () => {
      // Open modal
      const infoButton = element(by.type('RCTView').and(by.traits(['button'])).atIndex(0));
      await infoButton.tap();
      
      await waitFor(element(by.text('Modal')))
        .toBeVisible()
        .withTimeout(5000);
      
      const linkText = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      await expect(element(by.text(linkText))).toBeTappable();
    });
  });

  describe('Link Accessibility', () => {
    it('should have proper accessibility traits for external links', async () => {
      const linkText = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      // External links should be tappable/clickable
      await expect(element(by.text(linkText))).toBeTappable();
    });
  });
});
