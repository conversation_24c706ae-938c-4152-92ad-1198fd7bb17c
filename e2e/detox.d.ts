// Custom type definitions for Detox to fix TypeScript issues

declare module 'detox' {
  export interface DetoxExpectApi {
    toBeVisible(): Promise<void>;
    toBeNotVisible(): Promise<void>;
    toBeTappable(): Promise<void>;
    toBeNotTappable(): Promise<void>;
    toExist(): Promise<void>;
    toNotExist(): Promise<void>;
    toHaveText(text: string): Promise<void>;
    toHaveLabel(label: string): Promise<void>;
    toHaveId(id: string): Promise<void>;
    toHaveValue(value: string): Promise<void>;
  }

  export interface IndexableNativeElement {
    toBeVisible(): Promise<void>;
    toBeNotVisible(): Promise<void>;
    toBeTappable(): Promise<void>;
    toBeNotTappable(): Promise<void>;
    toExist(): Promise<void>;
    toNotExist(): Promise<void>;
    toHaveText(text: string): Promise<void>;
    toHaveLabel(label: string): Promise<void>;
    toHaveId(id: string): Promise<void>;
    toHaveValue(value: string): Promise<void>;
    tap(): Promise<void>;
    longPress(): Promise<void>;
    multiTap(times: number): Promise<void>;
    typeText(text: string): Promise<void>;
    replaceText(text: string): Promise<void>;
    clearText(): Promise<void>;
    scroll(pixels: number, direction?: string): Promise<void>;
    scrollTo(edge: string): Promise<void>;
    swipe(direction: string, speed?: string, percentage?: number): Promise<void>;
  }

  export interface NativeMatcher {
    id(id: string): NativeMatcher;
    text(text: string): NativeMatcher;
    label(label: string): NativeMatcher;
    type(type: string): NativeMatcher;
    traits(traits: string[]): NativeMatcher;
    value(value: string): NativeMatcher;
    and(matcher: NativeMatcher): NativeMatcher;
    or(matcher: NativeMatcher): NativeMatcher;
    not(matcher: NativeMatcher): NativeMatcher;
    atIndex(index: number): NativeMatcher;
  }

  export interface ByApi {
    id(id: string): NativeMatcher;
    text(text: string): NativeMatcher;
    label(label: string): NativeMatcher;
    type(type: string): NativeMatcher;
    traits(traits: string[]): NativeMatcher;
    value(value: string): NativeMatcher;
  }

  export interface ElementApi {
    (matcher: NativeMatcher): IndexableNativeElement;
  }

  export interface WaitForApi {
    (element: IndexableNativeElement): {
      toBeVisible(): {
        withTimeout(timeout: number): Promise<void>;
      };
      toBeNotVisible(): {
        withTimeout(timeout: number): Promise<void>;
      };
      toExist(): {
        withTimeout(timeout: number): Promise<void>;
      };
      toNotExist(): {
        withTimeout(timeout: number): Promise<void>;
      };
    };
  }

  export interface DeviceApi {
    launchApp(params?: { newInstance?: boolean }): Promise<void>;
    terminateApp(): Promise<void>;
    reloadReactNative(): Promise<void>;
    sendToHome(): Promise<void>;
    openURL(params: { url: string }): Promise<void>;
    setOrientation(orientation: 'portrait' | 'landscape'): Promise<void>;
    getPlatform(): 'ios' | 'android';
    pressBack(): Promise<void>;
  }

  export function expect(element: IndexableNativeElement): DetoxExpectApi;
  export const device: DeviceApi;
  export const element: ElementApi;
  export const by: ByApi;
  export const waitFor: WaitForApi;
}

declare namespace Detox {
  export type NativeMatcher = import('detox').NativeMatcher;
}
