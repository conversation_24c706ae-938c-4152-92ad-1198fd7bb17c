import { device, expect, element, by, waitFor } from 'detox';

describe('Debug E2E Tests', (): void => {
  beforeEach(async (): Promise<void> => {
    // Simple test to debug what's happening
  });

  describe('Basic App Launch', (): void => {
    it('should launch app and connect to development server', async (): Promise<void> => {
      // First, check if we're in the Expo Development Launcher
      try {
        await waitFor(element(by.text('Development Build')))
          .toBeVisible()
          .withTimeout(10000);

        console.log('✅ Found Expo Development Launcher');

        // Tap on the localhost development server
        await waitFor(element(by.text('http://localhost:8081')))
          .toBeVisible()
          .withTimeout(5000);

        console.log('✅ Found localhost server option');
        await element(by.text('http://localhost:8081')).tap();

        // Wait for our app to load
        await waitFor(element(by.text('Tab One')))
          .toBeVisible()
          .withTimeout(30000);

        console.log('✅ App loaded successfully!');
        await expect(element(by.text('Tab One'))).toBeVisible();

      } catch (error) {
        console.log('❌ Could not connect to development server');
        throw error;
      }
    });

    it('should find testID elements', async (): Promise<void> => {
      try {
        await waitFor(element(by.id('tab-one-screen')))
          .toBeVisible()
          .withTimeout(15000);
        
        console.log('✅ Found tab-one-screen testID');
        await expect(element(by.id('tab-one-screen'))).toBeVisible();
      } catch (error) {
        console.log('❌ Could not find tab-one-screen testID');
        
        // Try other testIDs
        try {
          await waitFor(element(by.id('info-button')))
            .toBeVisible()
            .withTimeout(5000);
          console.log('✅ Found info-button testID');
        } catch (e) {
          console.log('❌ Could not find info-button testID');
        }
        
        throw error;
      }
    });
  });
});
