# E2E Testing Setup for Taco App (TypeScript)

This directory contains end-to-end (e2e) tests for the Taco App using Detox framework with full TypeScript support.

## Test Structure

### Test Files (TypeScript)
- `app-navigation.test.ts` - Tests for app launch, tab navigation, and modal functionality
- `external-links.test.ts` - Tests for external link interactions
- `error-handling.test.ts` - Tests for error scenarios and app stability
- `ui-consistency.test.ts` - Tests for UI consistency and theming

### Helper Files
- `helpers.ts` - Common test utilities and helper functions
- `types.ts` - TypeScript type definitions and constants
- `init.ts` - Test setup and teardown

### Configuration Files
- `jest.config.js` - Jest configuration for e2e tests with TypeScript support
- `tsconfig.json` - TypeScript configuration for e2e tests
- `../detoxrc.js` - Detox configuration

## TypeScript Benefits

This e2e test suite is fully written in TypeScript, providing:

- ✅ **Type Safety**: Catch errors at compile time
- ✅ **IntelliSense**: Better IDE support with autocomplete
- ✅ **Refactoring**: Safe renaming and code restructuring
- ✅ **Documentation**: Self-documenting code with type definitions
- ✅ **Maintainability**: Easier to maintain and extend tests

### Key TypeScript Features Used
- Type definitions for test constants and configurations
- Helper classes with typed methods
- Strongly typed async/await patterns
- Interface definitions for test data structures

## Prerequisites

### iOS Testing
1. Install Xcode and iOS Simulator
2. Install Detox CLI: `npm install -g detox-cli`
3. Build the iOS app for testing

### Android Testing
1. Install Android Studio and Android SDK
2. Create an Android Virtual Device (AVD)
3. Install Detox CLI: `npm install -g detox-cli`
4. Build the Android app for testing

## Running Tests

### iOS
```bash
# Build the app for testing
npm run e2e:build:ios

# Run the tests
npm run e2e:test:ios
```

### Android
```bash
# Build the app for testing
npm run e2e:build:android

# Run the tests
npm run e2e:test:android
```

### Run specific test files
```bash
# Run only navigation tests
detox test --configuration ios.sim.debug e2e/app-navigation.test.js

# Run only UI consistency tests
detox test --configuration ios.sim.debug e2e/ui-consistency.test.js
```

## Test Coverage

### App Navigation
- ✅ App launch and initial load
- ✅ Tab switching between Tab One and Tab Two
- ✅ Modal opening and closing
- ✅ Content verification on all screens

### External Links
- ✅ External link presence and accessibility
- ✅ Link interaction (tappable state)

### Error Handling
- ✅ 404 error handling with deep linking
- ✅ App stability under stress (rapid navigation)
- ✅ Background/foreground transitions
- ✅ Device rotation handling

### UI Consistency
- ✅ Consistent styling across screens
- ✅ MonoText font rendering
- ✅ Layout and spacing consistency
- ✅ Interactive element functionality

## Test IDs Added

The following testIDs have been added to components for reliable testing:

- `tab-one-screen` - Main container for Tab One
- `tab-one-title` - Title text on Tab One
- `tab-one-separator` - Separator line on Tab One
- `tab-two-screen` - Main container for Tab Two
- `tab-two-title` - Title text on Tab Two
- `tab-two-separator` - Separator line on Tab Two
- `modal-screen` - Main container for Modal
- `modal-title` - Title text on Modal
- `modal-separator` - Separator line on Modal
- `info-button` - Info button in header

## Troubleshooting

### Common Issues

1. **"too many open files" error**
   - This is common on macOS. Increase file descriptor limit:
   ```bash
   ulimit -n 65536
   ```

2. **Simulator not found**
   - Make sure iOS Simulator is installed and the device name in `.detoxrc.js` matches available simulators
   - List available simulators: `xcrun simctl list devices`

3. **Android emulator issues**
   - Ensure AVD is created and running
   - Update AVD name in `.detoxrc.js` to match your emulator

4. **App build failures**
   - Make sure you can build and run the app normally first
   - Check that all dependencies are installed

### Debug Mode
Run tests with verbose output:
```bash
detox test --configuration ios.sim.debug --loglevel verbose
```

## Best Practices

1. **Use testIDs** instead of text selectors when possible for more reliable tests
2. **Add proper waits** for elements to appear before interacting
3. **Test on both platforms** (iOS and Android) as behavior can differ
4. **Keep tests independent** - each test should be able to run in isolation
5. **Use descriptive test names** that clearly indicate what is being tested

## Future Enhancements

- Add performance testing
- Add accessibility testing
- Add screenshot comparison tests
- Add network request mocking
- Add deep linking tests
- Add push notification tests
