import { device, expect, element, by, waitFor } from 'detox';
import { TestHelpers } from './helpers';
import { SCREEN_TEST_IDS, TEST_TEXTS, FILE_PATHS } from './types';

describe('App Navigation E2E Tests', (): void => {
  beforeEach(async (): Promise<void> => {
    await TestHelpers.reloadAndWait();
  });

  describe('App Launch', (): void => {
    it('should launch app successfully', async (): Promise<void> => {
      await TestHelpers.waitForTabOne();
      await expect(element(by.id(SCREEN_TEST_IDS.TAB_ONE_TITLE))).toBeVisible();
    });

    it('should display the main content on Tab One', async (): Promise<void> => {
      await expect(element(by.id(SCREEN_TEST_IDS.TAB_ONE_TITLE))).toBeVisible();
      await expect(element(by.text(TEST_TEXTS.OPEN_CODE_TEXT))).toBeVisible();
      await expect(element(by.text(FILE_PATHS.TAB_ONE_FILE))).toBeVisible();
    });
  });

  describe('Tab Navigation', (): void => {
    it('should navigate between tabs', async (): Promise<void> => {
      // Verify we start on Tab One
      await expect(element(by.text('Tab One'))).toBeVisible();
      
      // Navigate to Tab Two
      await element(by.text('Tab Two')).tap();
      
      // Verify Tab Two content is visible
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('app/(tabs)/two.tsx'))).toBeVisible();
      
      // Navigate back to Tab One
      await element(by.text('Tab One')).tap();
      
      // Verify Tab One content is visible again
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('app/(tabs)/index.tsx'))).toBeVisible();
    });

    it('should maintain tab state when switching', async (): Promise<void> => {
      // Start on Tab One
      await expect(element(by.text('Tab One'))).toBeVisible();
      
      // Switch to Tab Two
      await element(by.text('Tab Two')).tap();
      await expect(element(by.text('Tab Two'))).toBeVisible();
      
      // Switch back to Tab One
      await element(by.text('Tab One')).tap();
      await expect(element(by.text('Tab One'))).toBeVisible();
      
      // Content should still be there
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
    });
  });

  describe('Modal Navigation', (): void => {
    it('should open modal from info button', async (): Promise<void> => {
      // Wait for Tab One to be visible
      await waitFor(element(by.id('tab-one-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Find and tap the info button using testID
      const infoButton = element(by.id('info-button'));
      await waitFor(infoButton).toBeVisible().withTimeout(5000);
      await infoButton.tap();
      
      // Verify modal content is visible
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.id('modal-title'))).toBeVisible();
      await expect(element(by.text('app/modal.tsx'))).toBeVisible();
    });

    it('should close modal and return to main screen', async (): Promise<void> => {
      // Open modal first
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      // Verify modal is open
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Close modal (swipe down or back button depending on platform)
      if (device.getPlatform() === 'ios') {
        await element(by.id('modal-screen')).swipe('down', 'fast');
      } else {
        await device.pressBack();
      }
      
      // Verify we're back to the main screen
      await waitFor(element(by.id('tab-one-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Content Verification', (): void => {
    it('should display correct content on Tab One', async (): Promise<void> => {
      await expect(element(by.text('Tab One'))).toBeVisible();
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      await expect(element(by.text('app/(tabs)/index.tsx'))).toBeVisible();
      await expect(element(by.text('Change any of the text, save the file, and your app will automatically update.'))).toBeVisible();
    });

    it('should display correct content on Tab Two', async (): Promise<void> => {
      await element(by.text('Tab Two')).tap();
      
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      await expect(element(by.text('app/(tabs)/two.tsx'))).toBeVisible();
      await expect(element(by.text('Change any of the text, save the file, and your app will automatically update.'))).toBeVisible();
    });

    it('should display correct content in modal', async (): Promise<void> => {
      // Open modal
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      // Verify modal content
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text('Open up the code for this screen:'))).toBeVisible();
      await expect(element(by.text('app/modal.tsx'))).toBeVisible();
    });
  });
});
