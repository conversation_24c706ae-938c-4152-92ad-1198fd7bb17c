import { device, element, by, waitFor, expect } from 'detox';
import { SCREEN_TEST_IDS, TEST_TEXTS, TEST_CONFIG, Platform } from './types';

/**
 * Helper class for common E2E test operations
 */
export class TestHelpers {
  /**
   * Wait for an element to be visible with default timeout
   */
  static async waitForElement(elementMatcher: Detox.NativeMatcher, timeout: number = TEST_CONFIG.timeout): Promise<void> {
    await waitFor(element(elementMatcher))
      .toBeVisible()
      .withTimeout(timeout);
  }

  /**
   * Wait for Tab One screen to be visible
   */
  static async waitForTabOne(timeout: number = 15000): Promise<void> {
    await waitFor(element(by.id(SCREEN_TEST_IDS.TAB_ONE_SCREEN)))
      .toBeVisible()
      .withTimeout(timeout);
  }

  /**
   * Wait for Tab Two screen to be visible
   */
  static async waitForTabTwo(): Promise<void> {
    await this.waitForElement(by.id(SCREEN_TEST_IDS.TAB_TWO_SCREEN));
  }

  /**
   * Wait for Modal screen to be visible
   */
  static async waitForModal(): Promise<void> {
    await this.waitForElement(by.id(SCREEN_TEST_IDS.MODAL_SCREEN));
  }

  /**
   * Navigate to Tab Two
   */
  static async navigateToTabTwo(): Promise<void> {
    await element(by.text(TEST_TEXTS.TAB_TWO)).tap();
    await this.waitForTabTwo();
  }

  /**
   * Navigate to Tab One
   */
  static async navigateToTabOne(): Promise<void> {
    await element(by.text(TEST_TEXTS.TAB_ONE)).tap();
    await this.waitForTabOne();
  }

  /**
   * Open modal by tapping info button
   */
  static async openModal(): Promise<void> {
    const infoButton = element(by.id(SCREEN_TEST_IDS.INFO_BUTTON));
    await this.waitForElement(by.id(SCREEN_TEST_IDS.INFO_BUTTON));
    await infoButton.tap();
    await this.waitForModal();
  }

  /**
   * Close modal based on platform
   */
  static async closeModal(): Promise<void> {
    const platform: Platform = device.getPlatform() as Platform;
    
    if (platform === 'ios') {
      await element(by.id(SCREEN_TEST_IDS.MODAL_SCREEN)).swipe('down', 'fast');
    } else {
      await device.pressBack();
    }
    
    await this.waitForTabOne();
  }

  /**
   * Verify common screen elements
   */
  static async verifyScreenElements(screenTitle: string, filePath: string): Promise<void> {
    await expect(element(by.text(screenTitle))).toBeVisible();
    await expect(element(by.text(TEST_TEXTS.OPEN_CODE_TEXT))).toBeVisible();
    await expect(element(by.text(filePath))).toBeVisible();
    await expect(element(by.text(TEST_TEXTS.HELP_TEXT))).toBeVisible();
  }

  /**
   * Verify external link is present and tappable
   */
  static async verifyExternalLink(): Promise<void> {
    const linkElement = element(by.text(TEST_TEXTS.EXTERNAL_LINK_TEXT));
    await this.waitForElement(by.text(TEST_TEXTS.EXTERNAL_LINK_TEXT));
    await expect(linkElement).toBeVisible();
    await expect(linkElement).toBeTappable();
  }

  /**
   * Perform rapid tab switching for stress testing
   */
  static async performRapidTabSwitching(cycles: number = 5): Promise<void> {
    for (let i = 0; i < cycles; i++) {
      await this.navigateToTabTwo();
      await this.navigateToTabOne();
    }
  }

  /**
   * Perform modal open/close cycles for stress testing
   */
  static async performModalCycles(cycles: number = 3): Promise<void> {
    for (let i = 0; i < cycles; i++) {
      await this.openModal();
      await this.closeModal();
    }
  }

  /**
   * Test device rotation if supported
   */
  static async testDeviceRotation(): Promise<void> {
    try {
      await device.setOrientation('landscape');
      await this.waitForTabOne();
      
      await device.setOrientation('portrait');
      await this.waitForTabOne();
    } catch (error: unknown) {
      console.log('Device rotation test skipped - not supported in current environment');
    }
  }

  /**
   * Test background/foreground transition
   */
  static async testBackgroundForeground(): Promise<void> {
    await device.sendToHome();

    await new Promise<void>((resolve: () => void): void => {
      setTimeout(resolve, 1000);
    });

    await device.launchApp({ newInstance: false });
    await this.waitForTabOne();
  }

  /**
   * Test deep linking to non-existent route
   */
  static async testDeepLinkingError(): Promise<void> {
    try {
      await device.openURL({ url: 'tacoapp://non-existent-route' });
      
      await this.waitForElement(by.text(TEST_TEXTS.NOT_FOUND_TEXT));
      await expect(element(by.text(TEST_TEXTS.NOT_FOUND_TEXT))).toBeVisible();
      await expect(element(by.text(TEST_TEXTS.GO_HOME_TEXT))).toBeVisible();
      
      await element(by.text(TEST_TEXTS.GO_HOME_TEXT)).tap();
      await this.waitForTabOne();
    } catch (error: unknown) {
      console.log('Deep linking test skipped in test environment');
    }
  }

  /**
   * Connect to development server if needed and wait for app to be ready
   */
  static async connectToDevServerAndWait(): Promise<void> {
    try {
      // Check if we're in the Expo Development Launcher
      await waitFor(element(by.text('Development Build')))
        .toBeVisible()
        .withTimeout(5000);

      console.log('📱 Found Expo Development Launcher, connecting to dev server...');

      // Tap on the localhost development server
      await waitFor(element(by.text('http://localhost:8081')))
        .toBeVisible()
        .withTimeout(5000);

      await element(by.text('http://localhost:8081')).tap();

      // Wait for our app to load with a longer timeout
      await this.waitForTabOne(45000);

    } catch (error) {
      // If we don't find the development launcher, assume the app is already loaded
      console.log('📱 App already loaded or development launcher not found');
      await this.waitForTabOne();
    }
  }

  /**
   * Reload app and wait for it to be ready
   * For Expo apps, we use launchApp with newInstance: false instead of reloadReactNative
   */
  static async reloadAndWait(): Promise<void> => {
    await device.launchApp({ newInstance: false });
    await this.connectToDevServerAndWait();
  }

  /**
   * Get platform-specific timeout
   */
  static getPlatformTimeout(): number {
    const platform: Platform = device.getPlatform() as Platform;
    return platform === 'ios' ? TEST_CONFIG.timeout : TEST_CONFIG.timeout * 1.5;
  }
}
