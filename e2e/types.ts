// Type definitions for E2E tests

export interface TestConfig {
  timeout: number;
  retries: number;
}

export interface ScreenTestIds {
  TAB_ONE_SCREEN: 'tab-one-screen';
  TAB_ONE_TITLE: 'tab-one-title';
  TAB_ONE_SEPARATOR: 'tab-one-separator';
  TAB_TWO_SCREEN: 'tab-two-screen';
  TAB_TWO_TITLE: 'tab-two-title';
  TAB_TWO_SEPARATOR: 'tab-two-separator';
  MODAL_SCREEN: 'modal-screen';
  MODAL_TITLE: 'modal-title';
  MODAL_SEPARATOR: 'modal-separator';
  INFO_BUTTON: 'info-button';
}

export interface TestTexts {
  TAB_ONE: 'Tab One';
  TAB_TWO: 'Tab Two';
  MODAL: 'Modal';
  OPEN_CODE_TEXT: 'Open up the code for this screen:';
  HELP_TEXT: 'Change any of the text, save the file, and your app will automatically update.';
  EXTERNAL_LINK_TEXT: 'Tap here if your app doesn\'t automatically update after making changes';
  NOT_FOUND_TEXT: 'This screen doesn\'t exist.';
  GO_HOME_TEXT: 'Go to home screen!';
}

export interface FilePaths {
  TAB_ONE_FILE: 'app/(tabs)/index.tsx';
  TAB_TWO_FILE: 'app/(tabs)/two.tsx';
  MODAL_FILE: 'app/modal.tsx';
}

// Constants for test configuration
export const TEST_CONFIG: TestConfig = {
  timeout: 10000,
  retries: 3,
};

export const SCREEN_TEST_IDS: ScreenTestIds = {
  TAB_ONE_SCREEN: 'tab-one-screen',
  TAB_ONE_TITLE: 'tab-one-title',
  TAB_ONE_SEPARATOR: 'tab-one-separator',
  TAB_TWO_SCREEN: 'tab-two-screen',
  TAB_TWO_TITLE: 'tab-two-title',
  TAB_TWO_SEPARATOR: 'tab-two-separator',
  MODAL_SCREEN: 'modal-screen',
  MODAL_TITLE: 'modal-title',
  MODAL_SEPARATOR: 'modal-separator',
  INFO_BUTTON: 'info-button',
};

export const TEST_TEXTS: TestTexts = {
  TAB_ONE: 'Tab One',
  TAB_TWO: 'Tab Two',
  MODAL: 'Modal',
  OPEN_CODE_TEXT: 'Open up the code for this screen:',
  HELP_TEXT: 'Change any of the text, save the file, and your app will automatically update.',
  EXTERNAL_LINK_TEXT: 'Tap here if your app doesn\'t automatically update after making changes',
  NOT_FOUND_TEXT: 'This screen doesn\'t exist.',
  GO_HOME_TEXT: 'Go to home screen!',
};

export const FILE_PATHS: FilePaths = {
  TAB_ONE_FILE: 'app/(tabs)/index.tsx',
  TAB_TWO_FILE: 'app/(tabs)/two.tsx',
  MODAL_FILE: 'app/modal.tsx',
};

// Helper types for Detox
export type Platform = 'ios' | 'android';
export type Orientation = 'portrait' | 'landscape';
export type SwipeDirection = 'up' | 'down' | 'left' | 'right';
export type SwipeSpeed = 'slow' | 'fast';
