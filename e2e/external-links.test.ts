import { device, expect, element, by, waitFor } from 'detox';

describe('External Links E2E Tests', (): void => {
  beforeEach(async (): Promise<void> => {
    await device.reloadReactNative();
  });

  describe('External Link Interaction', (): void => {
    it('should find and interact with external link on Tab One', async (): Promise<void> => {
      await waitFor(element(by.text('Tab One')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Look for the external link text
      const linkText: string = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      
      // Note: We can't actually test the external link opening in e2e tests
      // as it would open a browser, but we can verify the link is tappable
      await expect(element(by.text(linkText))).toBeTappable();
    });

    it('should find external link on Tab Two', async (): Promise<void> => {
      await element(by.text('Tab Two')).tap();
      
      await waitFor(element(by.text('Tab Two')))
        .toBeVisible()
        .withTimeout(5000);
      
      const linkText: string = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      await expect(element(by.text(linkText))).toBeTappable();
    });

    it('should find external link in modal', async (): Promise<void> => {
      // Open modal
      const infoButton = element(by.id('info-button'));
      await infoButton.tap();
      
      await waitFor(element(by.id('modal-screen')))
        .toBeVisible()
        .withTimeout(5000);
      
      const linkText: string = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      await expect(element(by.text(linkText))).toBeVisible();
      await expect(element(by.text(linkText))).toBeTappable();
    });
  });

  describe('Link Accessibility', (): void => {
    it('should have proper accessibility traits for external links', async (): Promise<void> => {
      const linkText: string = 'Tap here if your app doesn\'t automatically update after making changes';
      
      await waitFor(element(by.text(linkText)))
        .toBeVisible()
        .withTimeout(5000);
      
      // External links should be tappable/clickable
      await expect(element(by.text(linkText))).toBeTappable();
    });
  });
});
