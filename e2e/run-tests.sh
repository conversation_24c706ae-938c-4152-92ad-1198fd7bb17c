#!/bin/bash

# E2E Test Runner for Taco App
# Usage: ./run-tests.sh [ios|android] [build|test|all]

set -e

PLATFORM=${1:-ios}
ACTION=${2:-all}

echo "🌮 Taco App E2E Test Runner"
echo "Platform: $PLATFORM"
echo "Action: $ACTION"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Detox CLI is installed
if ! command -v detox &> /dev/null; then
    print_error "Detox CLI is not installed. Please install it with: npm install -g detox-cli"
    exit 1
fi

# Check platform
if [[ "$PLATFORM" != "ios" && "$PLATFORM" != "android" ]]; then
    print_error "Invalid platform. Use 'ios' or 'android'"
    exit 1
fi

# Build function
build_app() {
    print_status "Building app for $PLATFORM..."
    
    if [[ "$PLATFORM" == "ios" ]]; then
        npm run e2e:build:ios
    else
        npm run e2e:build:android
    fi
    
    if [[ $? -eq 0 ]]; then
        print_status "Build completed successfully!"
    else
        print_error "Build failed!"
        exit 1
    fi
}

# Test function
run_tests() {
    print_status "Running e2e tests for $PLATFORM..."
    
    if [[ "$PLATFORM" == "ios" ]]; then
        npm run e2e:test:ios
    else
        npm run e2e:test:android
    fi
    
    if [[ $? -eq 0 ]]; then
        print_status "All tests passed! 🎉"
    else
        print_error "Some tests failed!"
        exit 1
    fi
}

# Main execution
case $ACTION in
    "build")
        build_app
        ;;
    "test")
        run_tests
        ;;
    "all")
        build_app
        echo ""
        run_tests
        ;;
    *)
        print_error "Invalid action. Use 'build', 'test', or 'all'"
        exit 1
        ;;
esac

print_status "E2E testing completed!"
