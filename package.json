{"name": "taco-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watchAll", "e2e:build:ios": "detox build --configuration ios.sim.debug", "e2e:test:ios": "detox test --configuration ios.sim.debug", "e2e:build:android": "detox build --configuration android.emu.debug", "e2e:test:android": "detox test --configuration android.emu.debug", "e2e:web": "playwright test", "e2e:web:ui": "playwright test --ui", "e2e:web:headed": "playwright test --headed", "e2e:web:debug": "playwright test --debug"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.6", "expo": "~53.0.11", "expo-dev-client": "~5.2.0", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@playwright/test": "^1.53.0", "@types/detox": "^17.14.3", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "detox": "^20.39.0", "jest": "^29.2.1", "jest-expo": "~53.0.7", "playwright": "^1.53.0", "react-test-renderer": "19.0.0", "ts-jest": "^29.3.4", "typescript": "~5.8.3"}, "private": true}