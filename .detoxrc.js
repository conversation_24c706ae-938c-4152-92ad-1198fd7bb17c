/** @type {Detox.DetoxConfig} */
module.exports = {
  testRunner: {
    args: {
      '$0': 'jest',
      config: 'e2e/jest.config.js'
    },
    jest: {
      setupFilesAfterEnv: ['<rootDir>/e2e/init.ts']
    }
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'SPECIFY_PATH_TO_YOUR_APP_BINARY',
      build: 'echo "Please build your Expo development build first"'
    },
    'ios.release': {
      type: 'ios.app',
      binaryPath: 'SPECIFY_PATH_TO_YOUR_APP_BINARY',
      build: 'echo "Please build your Expo development build first"'
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: 'SPECIFY_PATH_TO_YOUR_APK',
      build: 'echo "Please build your Expo development build first"'
    },
    'android.release': {
      type: 'android.apk',
      binaryPath: 'SPECIFY_PATH_TO_YOUR_APK',
      build: 'echo "Please build your Expo development build first"'
    }
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 16 Plus'
      }
    },
    attached: {
      type: 'android.attached',
      device: {
        adbName: '.*'
      }
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_3a_API_30_x86'
      }
    }
  },
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug'
    },
    'ios.sim.release': {
      device: 'simulator',
      app: 'ios.release'
    },
    'android.att.debug': {
      device: 'attached',
      app: 'android.debug'
    },
    'android.att.release': {
      device: 'attached',
      app: 'android.release'
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug'
    },
    'android.emu.release': {
      device: 'emulator',
      app: 'android.release'
    }
  }
};
