import { test, expect } from '@playwright/test';

test.describe('App Navigation E2E Tests (Web)', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Wait for the app to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('App Launch', () => {
    test('should launch app successfully', async ({ page }) => {
      // Wait for Tab One title to be visible using testID
      await expect(page.getByTestId('tab-one-title')).toBeVisible({ timeout: 10000 });
    });

    test('should display the main content on Tab One', async ({ page }) => {
      await expect(page.getByTestId('tab-one-title')).toBeVisible();
      await expect(page.getByText('Open up the code for this screen:')).toBeVisible();
      await expect(page.getByText('app/(tabs)/index.tsx')).toBeVisible();
    });
  });

  test.describe('Tab Navigation', () => {
    test('should navigate between tabs', async ({ page }) => {
      // Verify we start on Tab One
      await expect(page.getByTestId('tab-one-title')).toBeVisible();

      // Navigate to Tab Two using the tab button
      await page.getByRole('tab', { name: /Tab Two/ }).click();

      // Verify Tab Two content is visible
      await expect(page.getByTestId('tab-two-title')).toBeVisible();
      await expect(page.getByText('app/(tabs)/two.tsx')).toBeVisible();

      // Navigate back to Tab One
      await page.getByRole('tab', { name: /Tab One/ }).click();

      // Verify Tab One content is visible again
      await expect(page.getByTestId('tab-one-title')).toBeVisible();
      await expect(page.getByText('app/(tabs)/index.tsx')).toBeVisible();
    });

    test('should maintain tab state when switching', async ({ page }) => {
      // Start on Tab One
      await expect(page.getByTestId('tab-one-title')).toBeVisible();

      // Switch to Tab Two
      await page.getByRole('tab', { name: /Tab Two/ }).click();
      await expect(page.getByTestId('tab-two-title')).toBeVisible();

      // Switch back to Tab One
      await page.getByRole('tab', { name: /Tab One/ }).click();
      await expect(page.getByTestId('tab-one-title')).toBeVisible();

      // Content should still be there - check within Tab One screen
      await expect(page.getByTestId('tab-one-screen').getByText('Open up the code for this screen:')).toBeVisible();
    });
  });

  test.describe('Modal Navigation', () => {
    test('should open modal from info button', async ({ page }) => {
      // Wait for Tab One to be visible
      await expect(page.getByTestId('tab-one-title')).toBeVisible();

      // Find and click the info button using testID
      const infoButton = page.getByTestId('info-button');

      if (await infoButton.isVisible()) {
        await infoButton.click();

        // Verify modal content is visible
        await expect(page.getByTestId('modal-title')).toBeVisible({ timeout: 5000 });
        await expect(page.getByText('app/modal.tsx')).toBeVisible();
      } else {
        // Skip this test if info button is not found (might be different in web)
        test.skip();
      }
    });
  });

  test.describe('Content Verification', () => {
    test('should display correct content on Tab One', async ({ page }) => {
      await expect(page.getByTestId('tab-one-title')).toBeVisible();
      await expect(page.getByTestId('tab-one-screen').getByText('Open up the code for this screen:')).toBeVisible();
      await expect(page.getByText('app/(tabs)/index.tsx')).toBeVisible();
      await expect(page.getByTestId('tab-one-screen').getByText('Change any of the text, save the file, and your app will automatically update.')).toBeVisible();
    });

    test('should display correct content on Tab Two', async ({ page }) => {
      await page.getByRole('tab', { name: /Tab Two/ }).click();

      await expect(page.getByTestId('tab-two-title')).toBeVisible();
      await expect(page.getByTestId('tab-two-screen').getByText('Open up the code for this screen:')).toBeVisible();
      await expect(page.getByText('app/(tabs)/two.tsx')).toBeVisible();
      await expect(page.getByTestId('tab-two-screen').getByText('Change any of the text, save the file, and your app will automatically update.')).toBeVisible();
    });
  });

  test.describe('External Links', () => {
    test('should find and verify external link', async ({ page }) => {
      const linkText = 'Tap here if your app doesn\'t automatically update after making changes';
      
      // Check if the link is visible
      await expect(page.getByText(linkText)).toBeVisible();
      
      // Verify it's clickable (but don't actually click to avoid opening external browser)
      const link = page.getByText(linkText);
      await expect(link).toBeEnabled();
    });
  });

  test.describe('UI Consistency', () => {
    test('should display consistent styling across screens', async ({ page }) => {
      // Test Tab One styling
      await expect(page.getByTestId('tab-one-title')).toBeVisible();

      // Test Tab Two styling
      await page.getByRole('tab', { name: /Tab Two/ }).click();
      await expect(page.getByTestId('tab-two-title')).toBeVisible();

      // Both should have similar structure - check within Tab Two screen
      await expect(page.getByTestId('tab-two-screen').getByText('Open up the code for this screen:')).toBeVisible();
    });

    test('should display MonoText with correct styling', async ({ page }) => {
      // The file path should be displayed in monospace font
      const filePath = page.getByText('app/(tabs)/index.tsx');
      await expect(filePath).toBeVisible();

      // Check if it has monospace styling (this might need adjustment based on actual CSS)
      const fontFamily = await filePath.evaluate(el => getComputedStyle(el).fontFamily);
      expect(fontFamily).toContain('SpaceMono');
    });
  });
});
